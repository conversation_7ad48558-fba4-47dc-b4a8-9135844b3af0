class CountryInfo {
  final String code; // e.g., 'CM'
  final String name; // e.g., 'Cameroon'

  const CountryInfo({required this.code, required this.name});
}

// Global list of African countries
final List<CountryInfo> africanCountries = [
  const CountryInfo(code: 'DZ', name: 'Algeria'),
  const CountryInfo(code: 'AO', name: 'Angola'),
  const CountryInfo(code: 'BJ', name: 'Benin'),
  const CountryInfo(code: 'BW', name: 'Botswana'),
  const CountryInfo(code: 'BF', name: 'Burkina Faso'),
  const CountryInfo(code: 'BI', name: 'Burundi'),
  const CountryInfo(code: 'CV', name: 'Cabo Verde'),
  const CountryInfo(code: 'CM', name: 'Cameroon'),
  const CountryInfo(code: 'CF', name: 'Central African Republic'),
  const CountryInfo(code: 'TD', name: 'Chad'),
  const CountryInfo(code: 'KM', name: '<PERSON><PERSON>'),
  const CountryInfo(code: 'CD', name: 'Congo, Democratic Republic of the'),
  const CountryInfo(code: 'CG', name: 'Congo, Republic of the'),
  const CountryInfo(code: 'CI', name: "Cote d'Ivoire"),
  const CountryInfo(code: 'DJ', name: 'Djibouti'),
  const CountryInfo(code: 'EG', name: 'Egypt'),
  const CountryInfo(code: 'GQ', name: 'Equatorial Guinea'),
  const CountryInfo(code: 'ER', name: 'Eritrea'),
  const CountryInfo(code: 'SZ', name: 'Eswatini'),
  const CountryInfo(code: 'ET', name: 'Ethiopia'),
  const CountryInfo(code: 'GA', name: 'Gabon'),
  const CountryInfo(code: 'GM', name: 'Gambia'),
  const CountryInfo(code: 'GH', name: 'Ghana'),
  const CountryInfo(code: 'GN', name: 'Guinea'),
  const CountryInfo(code: 'GW', name: 'Guinea-Bissau'),
  const CountryInfo(code: 'KE', name: 'Kenya'),
  const CountryInfo(code: 'LS', name: 'Lesotho'),
  const CountryInfo(code: 'LR', name: 'Liberia'),
  const CountryInfo(code: 'LY', name: 'Libya'),
  const CountryInfo(code: 'MG', name: 'Madagascar'),
  const CountryInfo(code: 'MW', name: 'Malawi'),
  const CountryInfo(code: 'ML', name: 'Mali'),
  const CountryInfo(code: 'MR', name: 'Mauritania'),
  const CountryInfo(code: 'MU', name: 'Mauritius'),
  const CountryInfo(code: 'MA', name: 'Morocco'),
  const CountryInfo(code: 'MZ', name: 'Mozambique'),
  const CountryInfo(code: 'NA', name: 'Namibia'),
  const CountryInfo(code: 'NE', name: 'Niger'),
  const CountryInfo(code: 'NG', name: 'Nigeria'),
  const CountryInfo(code: 'RW', name: 'Rwanda'),
  const CountryInfo(code: 'ST', name: 'Sao Tome and Principe'),
  const CountryInfo(code: 'SN', name: 'Senegal'),
  const CountryInfo(code: 'SC', name: 'Seychelles'),
  const CountryInfo(code: 'SL', name: 'Sierra Leone'),
  const CountryInfo(code: 'SO', name: 'Somalia'),
  const CountryInfo(code: 'ZA', name: 'South Africa'),
  const CountryInfo(code: 'SS', name: 'South Sudan'),
  const CountryInfo(code: 'SD', name: 'Sudan'),
  const CountryInfo(code: 'TZ', name: 'Tanzania'),
  const CountryInfo(code: 'TG', name: 'Togo'),
  const CountryInfo(code: 'TN', name: 'Tunisia'),
  const CountryInfo(code: 'UG', name: 'Uganda'),
  const CountryInfo(code: 'ZM', name: 'Zambia'),
  const CountryInfo(code: 'ZW', name: 'Zimbabwe'),
  // Add other countries if needed, ensuring the code is the 2-letter ISO standard
];
