<!DOCTYPE html>
<html>

<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">

  <!-- SEO and General Meta Tags -->
  <title>Sniper Business Center</title>
  <meta name="description"
    content="Sniper Business Center is a platform that allows you to connect with other business owners and investors.">
  <meta name="keywords"
    content="business, networking, africa, marketplace, investment, community, professional, sniper business center, sbc">
  <meta name="author" content="Simb Technologies">

  <!-- Open Graph Meta Tags (for Facebook, LinkedIn, etc.) -->
  <meta property="og:title" content="Sniper Business Center">
  <meta property="og:description"
    content="Sniper Business Center is a platform that allows you to connect with other business owners and investors.">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://sniperbuisnesscenter.com/"> <!-- TODO: Replace with your app's URL -->
  <meta property="og:image" content="data:image/png;base64,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">
  <!-- TODO: Replace with a direct image URL -->
  <meta property="og:site_name" content="__APP_NAME_WEB__">
  <!-- <meta property="og:locale" content="en_US"> -->
  <!-- <meta property="og:locale:alternate" content="fr_FR"> -->

  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="__APP_NAME_WEB__">
  <meta name="twitter:description"
    content="Sniper Business Center is a platform that allows you to connect with other business owners and investors.">
  <meta name="twitter:image" content="data:image/png;base64,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">
  <!-- TODO: Replace with a direct image URL -->
  <!-- <meta name="twitter:site" content="@yourTwitterHandle"> --> <!-- Optional: Your Twitter handle -->
  <!-- <meta name="twitter:creator" content="@yourTwitterHandle"> -->
  <!-- Optional: Content creator's Twitter handle -->

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Sniper Business Center">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png" />

  <link rel="manifest" href="manifest.json">

  <script>
    // The value below is injected by flutter build, do not touch.
    var serviceWorkerVersion = null;
  </script>
  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js" defer></script>

  <!-- Script to replace placeholders with translations -->
  <script>
    function replaceMetaPlaceholders() {
      const lang = window.navigator.language.split('-')[0] || 'en'; // Default to 'en'
      let translations = {};

      // Fetch translations (simplified for this example)
      // In a real app, you would load your en.json/fr.json properly
      // This is a placeholder for how you might fetch or define them.
      const translationsEn = {
        "app_name_web": "Sniper Business Center - Your Business Network in Africa",
        "app_description_web": "Join Sniper Business Center, Africa's most dynamic business community. Connect, sell, invest, and grow your professional network.",
        "app_keywords_web": "business, networking, africa, marketplace, investment, community, professional, sniper business center, sbc",
        "app_author_web": "Simb Technologies"
      };
      const translationsFr = {
        "app_name_web": "Sniper Business Center - Votre Réseau d'Affaires en Afrique",
        "app_description_web": "Rejoignez Sniper Business Center, la communauté d'affaires la plus dynamique d'Afrique. Connectez-vous, vendez, investissez et développez votre réseau professionnel.",
        "app_keywords_web": "business, networking, afrique, marketplace, investissement, communauté, professionnel, sniper business center, sbc",
        "app_author_web": "Simb Technologies"
      };

      if (lang === 'fr') {
        translations = translationsFr;
      } else {
        translations = translationsEn;
      }

      document.title = translations.app_name_web || 'Sniper Business Center';
      document.querySelector('meta[name="description"]').setAttribute('content', translations.app_description_web || 'Africa\'s Business Hub');
      document.querySelector('meta[name="keywords"]').setAttribute('content', translations.app_keywords_web || 'business, africa');
      document.querySelector('meta[name="author"]').setAttribute('content', translations.app_author_web || 'Simb Technologies');

      document.querySelector('meta[property="og:title"]').setAttribute('content', translations.app_name_web || 'Sniper Business Center');
      document.querySelector('meta[property="og:description"]').setAttribute('content', translations.app_description_web || 'Africa\'s Business Hub');
      document.querySelector('meta[property="og:site_name"]').setAttribute('content', translations.app_name_web || 'Sniper Business Center');

      document.querySelector('meta[name="twitter:title"]').setAttribute('content', translations.app_name_web || 'Sniper Business Center');
      document.querySelector('meta[name="twitter:description"]').setAttribute('content', translations.app_description_web || 'Africa\'s Business Hub');
      document.querySelector('meta[name="apple-mobile-web-app-title"]').setAttribute('content', translations.app_name_web || 'Sniper Business Center');
    }
    window.addEventListener('load', replaceMetaPlaceholders);
  </script>

</head>

<body>
  <script>
    window.addEventListener('load', function (ev) {
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        onEntrypointLoaded: function (engineInitializer) {
          engineInitializer.initializeEngine().then(function (appRunner) {
            appRunner.runApp();
          });
        }
      });
    });
  </script>
</body>

</html>