name: snipper_frontend
description: "The most dynamic business center in the world"

publish_to: 'none' # Remove this line if you wish to publish to pub.dev


version: 1.0.0+1

environment:
  sdk: '>=3.5.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter


  cupertino_icons: ^1.0.2
  google_fonts: ^4.0.4
  http:
  shared_preferences:
  jwt_decoder:
  modal_progress_hud_nsn: ^0.5.0
  file_picker: ^5.3.3
  open_file: ^3.3.2
  path_provider: ^2.0.13
  url_launcher: ^6.1.6
  intl_phone_field: ^3.1.0
  google_nav_bar: ^5.0.6
  chewie: ^1.7.4
  video_player: ^2.8.2
  another_carousel_pro: ^1.0.2
  permission_handler: ^9.2.0
  flutter_otp_text_field: ^1.1.1
  contacts_service: ^0.6.3
  flutter_svg: ^2.0.10+1
  flutter_rating_bar: ^4.0.1
  dotted_border: ^2.1.0
  flutter_speed_dial: ^7.0.0
  go_router: ^14.4.1
  flutter_localization: ^0.2.2
  share_plus: ^10.1.2
  smooth_page_indicator: ^1.2.1
  flutter_riverpod: ^2.6.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: "^0.13.1"

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/app_logo.png"
  flutter_lints: ^2.0.0


flutter:


  uses-material-design: true

  assets:
    - assets/design/images/
    - assets/assets/images/
    - assets/videos/
    - assets/videos/Sniper business center contacts.vcf
    - assets/slides/
    - assets/icons/
    - assets/lang/en.json
    - assets/lang/fr.json

